import os
from openai import AzureOpenAI

# Set your API key as an environment variable for security
client = AzureOpenAI(
    api_key="7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL",  # Set this in your environment
    api_version="2024-02-01",  # More recent version
    azure_endpoint="https://mentalhealth-bot.openai.azure.com/"
)

response = client.chat.completions.create(
    model="gpt4o-deployment",  # Your deployment name
    messages=[
        {"role": "system", "content": "You are a JSON-generating assistant."},
        {"role": "user", "content": "Give me a summary in JSON."}
    ],
    temperature=0.1,
    max_tokens=850,
    top_p=1.0,
    frequency_penalty=0.0,
    presence_penalty=0.0,
    response_format={"type": "json_object"}  # ✅ Correct format
)

print(response.choices[0].message.content)

from openai import AzureOpenAI

client = AzureOpenAI(
    api_key="7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL",
    api_version="2023-12-01-preview",  # latest for GPT-4o, JSON format, etc.
    azure_endpoint="https://mentalhealth-bot.openai.azure.com/"
)

response = client.chat.completions.create(
    model="gpt4o-deployment",  # 🔁 Use your Azure deployment name here, NOT "gpt-4o"
    messages=[
        {"role": "system", "content": "You are a JSON-generating assistant."},
        {"role": "user", "content": "Give me a summary in JSON."}
    ],
    temperature=0.1,
    max_tokens=850,
    top_p=1.0,
    frequency_penalty=0.0,
    presence_penalty=0.0,
    response_format="json_object"  # ✅ string, not dict
)

print(response.choices[0].message.content)


from openai import AzureOpenAI

client = AzureOpenAI(
    api_key="7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL",
    api_version="2024-11-20",  # or latest version
    azure_endpoint="https://mentalhealth-bot.openai.azure.com/"
)

response = client.chat.completions.create(
    model="gpt-4o",  # This should match your deployment name
    messages="Hello",
    temperature=0.1,
    max_tokens=850,
    top_p=1.0,
    frequency_penalty=0.0,
    presence_penalty=0.0,
    response_format={"type": "json_object"}
)

import requests  
  
endpoint = "https://mentalhealth-bot.openai.azure.com/openai/deployments/<deployment-name>/completions?api-version=2023-03-15-preview"  
api_key = "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL"  
  
headers = {  
    "Content-Type": "application/json",  
    "api-key": api_key  
}  
  
data = {  
    "prompt": "Write a short story about AI and humanity.",  
    "max_tokens": 100,  
    "temperature": 0.7  
}  
  
response = requests.post(endpoint, headers=headers, json=data)  
  
if response.status_code == 200:  
    print(response.json())  
else:  
    print(f"Error: {response.status_code}, {response.text}")  


import os
import base64
from openai import AzureOpenAI

endpoint = os.getenv("ENDPOINT_URL", "https://mentalhealth-bot.openai.azure.com/")
deployment = os.getenv("DEPLOYMENT_NAME", "gpt-4o")
subscription_key = os.getenv("AZURE_OPENAI_API_KEY", "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL")

# Initialize Azure OpenAI client with key-based authentication
client = AzureOpenAI(
    azure_endpoint=endpoint,
    api_key=subscription_key,
    api_version="2025-01-01-preview",
)

# IMAGE_PATH = "YOUR_IMAGE_PATH"
# encoded_image = base64.b64encode(open(IMAGE_PATH, 'rb').read()).decode('ascii')

#Prepare the chat prompt
chat_prompt = [
    {
        "role": "system",
        "content": [
            {
                "type": "text",
                "text": "You are an AI assistant that helps people find information."
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "I am going to Paris, what should I see?"
            }
        ]
    },
    {
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": "Paris, the capital of France, is known for its stunning architecture, art museums, historical landmarks, and romantic atmosphere. Here are some of the top attractions to see in Paris:\n\n1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable landmarks in the world and offers breathtaking views of the city.\n2. The Louvre Museum: The Louvre is one of the world's largest and most famous museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\n3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks in Paris and is known for its Gothic architecture and stunning stained glass windows.\n\nThese are just a few of the many attractions that Paris has to offer. With so much to see and do, it's no wonder that Paris is one of the most popular tourist destinations in the world."
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "What is so great about #1?"
            }
        ]
    }
]

# Include speech result if speech is enabled
messages = chat_prompt

# Generate the completion
completion = client.chat.completions.create(
    model=deployment,
    messages=messages,
    max_tokens=800,
    temperature=0.7,
    top_p=0.95,
    frequency_penalty=0,
    presence_penalty=0,
    stop=None,
    stream=True
)

print(completion)
    

import os  
from openai import AzureOpenAI  
  
# Set up environment variables or direct values  
endpoint = os.getenv("ENDPOINT_URL", "https://mentalhealth-bot.openai.azure.com/")
deployment = os.getenv("DEPLOYMENT_NAME", "gpt-4o")
subscription_key = os.getenv("AZURE_OPENAI_API_KEY", "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL")

  
# Initialize Azure OpenAI client  
client = AzureOpenAI(  
    azure_endpoint=endpoint,  
    api_key=subscription_key,  
    api_version="2025-01-01-preview",  
)  
  
# Prepare the chat prompt  
chat_prompt = [  
    {  
        "role": "system",  
        "content": "You are an AI assistant that helps people find information.",  
    },  
    {  
        "role": "user",  
        "content": "I am going to Paris, what should I see?",  
    },  
    {  
        "role": "assistant",  
        "content": (  
            "Paris, the capital of France, is known for its stunning architecture, "  
            "art museums, historical landmarks, and romantic atmosphere. Here are "  
            "some of the top attractions to see in Paris:\n\n"  
            "1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable "  
            "landmarks in the world and offers breathtaking views of the city.\n"  
            "2. The Louvre Museum: The Louvre is one of the world's largest and most famous "  
            "museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\n"  
            "3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks "  
            "in Paris and is known for its Gothic architecture and stunning stained glass windows.\n\n"  
            "These are just a few of the many attractions that Paris has to offer. With so much to see "  
            "and do, it's no wonder that Paris is one of the most popular tourist destinations in the world."  
        ),  
    },  
    {  
        "role": "user",  
        "content": "What is so great about #1?",  
    },  
]  
  
# Generate the completion with streaming  
response = client.chat.completions.create(  
    model=deployment,  
    messages=chat_prompt,  
    max_tokens=800,  
    temperature=0.7,  
    top_p=0.95,  
    frequency_penalty=0,  
    presence_penalty=0,  
    stop=None,  
    stream=True,  # Enable streaming  
)  
  
# Handle the streaming response  
print("Streaming response:")  
for chunk in response:  # Stream yields chunks of data  
    if "choices" in chunk and chunk["choices"]:  
        for choice in chunk["choices"]:  
            if "delta" in choice and "content" in choice["delta"]:  
                print(choice["delta"]["content"], end="", flush=True)  
print()  # Add a newline at the end  

import os  
from openai import AzureOpenAI  
  
# Set up environment variables or direct values  
endpoint = os.getenv("ENDPOINT_URL", "https://mentalhealth-bot.openai.azure.com/")
deployment = os.getenv("DEPLOYMENT_NAME", "gpt-4o")
subscription_key = os.getenv("AZURE_OPENAI_API_KEY", "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL")

# Initialize Azure OpenAI client  
client = AzureOpenAI(  
    azure_endpoint=endpoint,  
    api_key=subscription_key,  
    api_version="2024-11-20",  # Use stable API version
)  
  
# Prepare the chat prompt  
chat_prompt = [  
    {  
        "role": "system",  
        "content": "You are an AI assistant that helps people find information.",  
    },  
    {  
        "role": "user",  
        "content": "I am going to Paris, what should I see?",  
    },  
    {  
        "role": "assistant",  
        "content": (  
            "Paris, the capital of France, is known for its stunning architecture, "  
            "art museums, historical landmarks, and romantic atmosphere. Here are "  
            "some of the top attractions to see in Paris:\n\n"  
            "1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable "  
            "landmarks in the world and offers breathtaking views of the city.\n"  
            "2. The Louvre Museum: The Louvre is one of the world's largest and most famous "  
            "museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\n"  
            "3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks "  
            "in Paris and is known for its Gothic architecture and stunning stained glass windows.\n\n"  
            "These are just a few of the many attractions that Paris has to offer. With so much to see "  
            "and do, it's no wonder that Paris is one of the most popular tourist destinations in the world."  
        ),  
    },  
    {  
        "role": "user",  
        "content": "What is so great about #1?",  
    },  
]  

def stream_chat_response():
    """Function to handle streaming chat response"""
    try:
        # Generate the completion with streaming  
        response = client.chat.completions.create(  
            model=deployment,  
            messages=chat_prompt,  
            max_tokens=800,  
            temperature=0.7,  
            top_p=0.95,  
            frequency_penalty=0,  
            presence_penalty=0,  
            stop=None,  
            stream=True,  # Enable streaming  
        )  
        
        # Handle the streaming response  
        print("Streaming response:")  
        print("-" * 50)
        
        collected_content = ""
        
        for chunk in response:  
            # Check if chunk has choices and content
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if hasattr(delta, 'content') and delta.content:
                    content = delta.content
                    print(content, end="", flush=True)
                    collected_content += content
        
        print("\n" + "-" * 50)
        print("Streaming complete!")
        
        return collected_content
        
    except Exception as e:
        print(f"Error during streaming: {e}")
        return None

# Alternative method using dictionary access (if the above doesn't work)
def stream_chat_response_alt():
    """Alternative streaming method using dictionary access"""
    try:
        response = client.chat.completions.create(  
            model=deployment,  
            messages=chat_prompt,  
            max_tokens=800,  
            temperature=0.7,  
            top_p=0.95,  
            frequency_penalty=0,  
            presence_penalty=0,  
            stop=None,  
            stream=True,  
        )  
        
        print("Streaming response (Alternative method):")  
        print("-" * 50)
        
        for chunk in response:
            chunk_dict = chunk.model_dump()  # Convert to dictionary
            if chunk_dict.get("choices"):
                for choice in chunk_dict["choices"]:
                    delta = choice.get("delta", {})
                    if "content" in delta and delta["content"]:
                        print(delta["content"], end="", flush=True)
        
        print("\n" + "-" * 50)
        print("Streaming complete!")
        
    except Exception as e:
        print(f"Error during streaming: {e}")

if __name__ == "__main__":
    # Try the first method
    print("Starting streaming chat...")
    result = stream_chat_response()
    
    # If first method fails, try alternative
    if result is None:
        print("\nTrying alternative streaming method...")
        stream_chat_response_alt()

import os  
from openai import AzureOpenAI  



print("=== DEBUGGING AZURE OPENAI CONNECTION ===")
print(f"Endpoint: {endpoint}")
print(f"Deployment: {deployment}")
print(f"API Key: {subscription_key[:10]}..." if subscription_key else "No API Key")
print()

# Step 1: Test basic connection and list available models/deployments
def test_connection():
    """Test basic connection to Azure OpenAI"""
    try:
        client = AzureOpenAI(  
            azure_endpoint=endpoint,  
            api_key=subscription_key,  
            api_version="2024-11-20",
        )
        
        print("✅ Client initialized successfully")
        
        # Try to list available models/deployments
        print("\n=== AVAILABLE DEPLOYMENTS ===")
        try:
            models = client.models.list()
            print("Available deployments:")
            for model in models:
                print(f"  - {model.id}")
            return client, [model.id for model in models]
        except Exception as e:
            print(f"❌ Could not list deployments: {e}")
            return client, []
            
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return None, []

# Step 2: Test different common deployment names
def test_deployment_names(client, available_deployments):
    """Test common deployment names"""
    if not client:
        return None
        
    # Common deployment names to try
    common_names = [
        "gpt-4o",
        "gpt4o", 
        "gpt-4o-2024-11-20",
        "gpt-4o-deployment",
        "openai-gpt-4o",
        "gpt-35-turbo",  # Fallback option
        "gpt-35-turbo-16k"  # Another fallback
    ]
    
    # Add available deployments to test list
    all_names_to_test = list(set(available_deployments + common_names))
    
    print(f"\n=== TESTING DEPLOYMENT NAMES ===")
    working_deployments = []
    
    for name in all_names_to_test:
        try:
            print(f"Testing '{name}'...", end=" ")
            
            # Try a simple completion
            response = client.chat.completions.create(
                model=name,
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=5,
                stream=False
            )
            
            print("✅ WORKS!")
            working_deployments.append(name)
            
        except Exception as e:
            error_msg = str(e)
            if "404" in error_msg or "DeploymentNotFound" in error_msg:
                print("❌ Not found")
            else:
                print(f"❌ Error: {error_msg}")
    
    return working_deployments

# Step 3: Test streaming with working deployment
def test_streaming(client, deployment_name):
    """Test streaming with a working deployment"""
    if not client or not deployment_name:
        return False
        
    print(f"\n=== TESTING STREAMING WITH '{deployment_name}' ===")
    try:
        response = client.chat.completions.create(
            model=deployment_name,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say hello in a friendly way"}
            ],
            max_tokens=50,
            temperature=0.7,
            stream=True
        )
        
        print("Streaming test:")
        print("-" * 30)
        
        for chunk in response:
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if hasattr(delta, 'content') and delta.content:
                    print(delta.content, end="", flush=True)
        
        print("\n" + "-" * 30)
        print("✅ Streaming works!")
        return True
        
    except Exception as e:
        print(f"❌ Streaming failed: {e}")
        return False

# Main execution
if __name__ == "__main__":
    # Step 1: Test connection
    client, available_deployments = test_connection()
    
    if not client:
        print("\n❌ CONNECTION FAILED!")
        print("\nPlease check:")
        print("1. Your API key is correct and active")
        print("2. Your endpoint URL is correct")
        print("3. Your Azure OpenAI resource exists and is accessible")
        exit(1)
    
    # Step 2: Find working deployments
    working_deployments = test_deployment_names(client, available_deployments)
    
    if not working_deployments:
        print("\n❌ NO WORKING DEPLOYMENTS FOUND!")
        print("\nYou need to create a deployment in Azure Portal:")
        print("1. Go to Azure Portal → Your OpenAI Resource")
        print("2. Navigate to 'Model deployments' or 'Deployments'")
        print("3. Click '+ Create new deployment'")
        print("4. Select GPT-4o model and create deployment")
        print("5. Note the deployment name and use it in your code")
    else:
        print(f"\n✅ WORKING DEPLOYMENTS FOUND: {working_deployments}")
        
        # Step 3: Test streaming with first working deployment
        best_deployment = working_deployments[0]
        streaming_works = test_streaming(client, best_deployment)
        
        if streaming_works:
            print(f"\n🎉 SUCCESS! Use deployment name: '{best_deployment}'")
            print("\nUpdate your code with:")
            print(f"deployment = '{best_deployment}'")
        else:
            print(f"\n⚠️  Deployment works but streaming failed with '{best_deployment}'")
            print("Try using stream=False for non-streaming responses")

import os  
from openai import AzureOpenAI  
  
# Your working deployment names
endpoint = "https://mentalhealth-bot.openai.azure.com/"
deployment = "gpt-4o"  # or "gpt-4o-2"
# subscription_key = "your-actual-api-key"  # Make sure this is current
subscription_key = os.getenv("AZURE_OPENAI_API_KEY", "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL")


client = AzureOpenAI(  
    azure_endpoint=endpoint,  
    api_key=subscription_key,  
    api_version="2024-11-20",
)

# Simple test first
try:
    response = client.chat.completions.create(
        model=deployment,
        messages=[{"role": "user", "content": "Hi"}],
        max_tokens=10
    )
    print("✅ Connection works!")
    print(f"Response: {response.choices[0].message.content}")
except Exception as e:
    print(f"❌ Error: {e}")

import os  
from openai import AzureOpenAI  
  
# Set up environment variables or direct values  
endpoint = os.getenv("ENDPOINT_URL", "https://mentalhealth-bot.openai.azure.com/")
deployment = os.getenv("DEPLOYMENT_NAME", "gpt-4o")  # Try "gpt-4o" or "gpt-4o-2"
# subscription_key = os.getenv("AZURE_OPENAI_API_KEY", "your-api-key-here")  # Use environment variable for security

# Initialize Azure OpenAI client  
client = AzureOpenAI(  
    azure_endpoint=endpoint,  
    api_key=subscription_key,  
    api_version="2024-11-20",  # Use stable API version
)  
  
# Prepare the chat prompt  
chat_prompt = [  
    {  
        "role": "system",  
        "content": "You are an AI assistant that helps people find information.",  
    },  
    {  
        "role": "user",  
        "content": "I am going to Paris, what should I see?",  
    },  
    {  
        "role": "assistant",  
        "content": (  
            "Paris, the capital of France, is known for its stunning architecture, "  
            "art museums, historical landmarks, and romantic atmosphere. Here are "  
            "some of the top attractions to see in Paris:\n\n"  
            "1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable "  
            "landmarks in the world and offers breathtaking views of the city.\n"  
            "2. The Louvre Museum: The Louvre is one of the world's largest and most famous "  
            "museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\n"  
            "3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks "  
            "in Paris and is known for its Gothic architecture and stunning stained glass windows.\n\n"  
            "These are just a few of the many attractions that Paris has to offer. With so much to see "  
            "and do, it's no wonder that Paris is one of the most popular tourist destinations in the world."  
        ),  
    },  
    {  
        "role": "user",  
        "content": "What is so great about #1?",  
    },  
]  

def stream_chat_response():
    """Function to handle streaming chat response"""
    try:
        # Generate the completion with streaming  
        response = client.chat.completions.create(  
            model=deployment,  
            messages=chat_prompt,  
            max_tokens=800,  
            temperature=0.7,  
            top_p=0.95,  
            frequency_penalty=0,  
            presence_penalty=0,  
            stop=None,  
            stream=True,  # Enable streaming  
        )  
        
        # Handle the streaming response  
        print("Streaming response:")  
        print("-" * 50)
        
        collected_content = ""
        
        for chunk in response:  
            # Check if chunk has choices and content
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if hasattr(delta, 'content') and delta.content:
                    content = delta.content
                    print(content, end="", flush=True)
                    collected_content += content
        
        print("\n" + "-" * 50)
        print("Streaming complete!")
        
        return collected_content
        
    except Exception as e:
        print(f"Error during streaming: {e}")
        return None

# Alternative method using dictionary access (if the above doesn't work)
def stream_chat_response_alt():
    """Alternative streaming method using dictionary access"""
    try:
        response = client.chat.completions.create(  
            model=deployment,  
            messages=chat_prompt,  
            max_tokens=800,  
            temperature=0.7,  
            top_p=0.95,  
            frequency_penalty=0,  
            presence_penalty=0,  
            stop=None,  
            stream=True,  
        )  
        
        print("Streaming response (Alternative method):")  
        print("-" * 50)
        
        for chunk in response:
            chunk_dict = chunk.model_dump()  # Convert to dictionary
            if chunk_dict.get("choices"):
                for choice in chunk_dict["choices"]:
                    delta = choice.get("delta", {})
                    if "content" in delta and delta["content"]:
                        print(delta["content"], end="", flush=True)
        
        print("\n" + "-" * 50)
        print("Streaming complete!")
        
    except Exception as e:
        print(f"Error during streaming: {e}")

if __name__ == "__main__":
    # Try the first method
    print("Starting streaming chat...")
    result = stream_chat_response()
    
    # If first method fails, try alternative
    if result is None:
        print("\nTrying alternative streaming method...")
        stream_chat_response_alt()

import os
import base64
import asyncio
from openai import AsyncAzureOpenAI
from azure.identity.aio import DefaultAzureCredential, get_bearer_token_provider

async def main() -> None:
    """
    When prompted for user input, type a message and hit enter to send it to the model.
    Enter "q" to quit the conversation.
    """

    # client = AsyncAzureOpenAI(
    #     azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT"],
    #     api_key=os.environ["AZURE_OPENAI_API_KEY"],
    #     api_version="2025-04-01-preview",
    # )
    async with client.beta.realtime.connect(
        model="gpt-4o-realtime-preview",  # deployment name of your model
    ) as connection:
        await connection.session.update(session={"modalities": ["text", "audio"]})  
        while True:
            user_input = input("Enter a message: ")
            if user_input == "q":
                break

            await connection.conversation.item.create(
                item={
                    "type": "message",
                    "role": "user",
                    "content": [{"type": "input_text", "text": user_input}],
                }
            )
            await connection.response.create()
            async for event in connection:
                if event.type == "response.text.delta":
                    print(event.delta, flush=True, end="")
                elif event.type == "response.audio.delta":

                    audio_data = base64.b64decode(event.delta)
                    print(f"Received {len(audio_data)} bytes of audio data.")
                elif event.type == "response.audio_transcript.delta":
                    print(f"Received text delta: {event.delta}")
                elif event.type == "response.text.done":
                    print()
                elif event.type == "response.done":
                    break

asyncio.run(main())

import os
import base64
import asyncio
from openai import AsyncAzureOpenAI

async def main() -> None:
    """
    Azure OpenAI Realtime API example for text and audio conversations.
    Enter "q" to quit the conversation.
    """
    
    # Initialize the Azure OpenAI client
    client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT", "https://mentalhealth-bot.openai.azure.com/"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY", "your-api-key-here"),
        api_version="2024-10-01-preview",  # Use correct API version for Realtime
    )
    
    print("🎤 Azure OpenAI Realtime API Chat")
    print("Enter 'q' to quit")
    print("-" * 40)
    
    try:
        # Connect to the realtime API
        async with client.beta.realtime.connect(
            model="gpt-4o-realtime-preview",  # Your realtime deployment name
        ) as connection:
            
            # Configure session for text and audio
            await connection.session.update(
                session={
                    "modalities": ["text", "audio"],
                    "voice": "alloy",  # Options: alloy, echo, fable, onyx, nova, shimmer
                    "turn_detection": {"type": "server_vad"},  # Voice activity detection
                    "input_audio_format": "pcm16",
                    "output_audio_format": "pcm16",
                    "temperature": 0.8,
                }
            )
            
            print("✅ Connected to Realtime API")
            
            while True:
                try:
                    user_input = input("\n💬 You: ")
                    if user_input.lower() == "q":
                        print("👋 Goodbye!")
                        break
                    
                    if not user_input.strip():
                        continue
                    
                    # Send user message
                    await connection.conversation.item.create(
                        item={
                            "type": "message",
                            "role": "user",
                            "content": [{"type": "input_text", "text": user_input}],
                        }
                    )
                    
                    # Request response
                    await connection.response.create()
                    
                    print("🤖 Assistant: ", end="", flush=True)
                    
                    # Handle streaming response
                    async for event in connection:
                        if event.type == "response.text.delta":
                            # Stream text response
                            print(event.delta, end="", flush=True)
                            
                        elif event.type == "response.audio.delta":
                            # Handle audio data (if you want to play it)
                            audio_data = base64.b64decode(event.delta)
                            print(f"\n🔊 [Received {len(audio_data)} bytes of audio]", end="")
                            # TODO: Add audio playback here if needed
                            
                        elif event.type == "response.audio_transcript.delta":
                            # Audio transcript (what the AI is saying)
                            print(f"🎙️ [Audio transcript: {event.delta}]", end="")
                            
                        elif event.type == "response.text.done":
                            # Text response completed
                            print()  # New line after text is done
                            
                        elif event.type == "response.done":
                            # Full response completed
                            break
                            
                        elif event.type == "error":
                            print(f"\n❌ Error: {event.error}")
                            break
                            
                except KeyboardInterrupt:
                    print("\n👋 Interrupted by user")
                    break
                except Exception as e:
                    print(f"\n❌ Error during conversation: {e}")
                    continue
                    
    except Exception as e:
        print(f"❌ Failed to connect to Realtime API: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you have a 'gpt-4o-realtime-preview' deployment")
        print("2. Check your API key and endpoint")
        print("3. Verify the API version supports Realtime API")
        print("4. Ensure your Azure region supports Realtime API")

# Alternative version for testing regular chat (non-realtime)
async def test_regular_chat():
    """Test regular Azure OpenAI chat to verify connection"""
    client = AsyncAzureOpenAI(
        azure_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT", "https://mentalhealth-bot.openai.azure.com/"),
        api_key=os.environ.get("AZURE_OPENAI_API_KEY", "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL"),
        api_version="2024-11-20",
    )
    
    try:
        response = await client.chat.completions.create(
            model="gpt-4o",  # Use your regular deployment name
            messages=[{"role": "user", "content": "Hello, this is a test"}],
            max_tokens=50
        )
        print("✅ Regular chat works!")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Regular chat failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing connection first...")
    
    # Test regular chat first
    if asyncio.run(test_regular_chat()):
        print("\n" + "="*50)
        print("Starting Realtime API...")
        asyncio.run(main())
    else:
        print("\n❌ Basic connection failed. Fix regular chat first before trying Realtime API.")

from openai import AzureOpenAI

OPENAI_CLIENT = AzureOpenAI(
    api_key="3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7",
    azure_endpoint="https://rohan-mcj7eabj-swedencentral.cognitiveservices.azure.com/",
    api_version="2025-03-01-preview"
)

response = OPENAI_CLIENT.audio.speech.create(
    model="tts",          # must match your deployment name
    voice="nova",
    input="Hello Sneha, this is Dr. Tova again. Let's talk about your progress today."
)

with open("dr_tova_response.mp3", "wb") as f:
    f.write(response.content)

print("✅ Audio saved to dr_tova_response.mp3")


import asyncio
from elevenlabs.client import ElevenLabs

client = ElevenLabs(
    api_key="***************************************************",  # 🔑
)


# 🎤 Set voice and model
voice_id = "pMsXgVXv3BLzUgSXRplE"  # Replace with the actual voice ID
model_id = "eleven_multilingual_v2"
output_format = "mp3_44100_128"  # common output format

# 🧠 Text to convert
text_to_convert = "Hello Sneha, this is Dr. Tova. How are you feeling today?"

# 🔁 Stream and save the audio
output_path = "output.mp3"
with open(output_path, "wb") as f:
    for chunk in client.text_to_speech.stream(
        voice_id=voice_id,
        output_format=output_format,
        text=text_to_convert,
        model_id=model_id
    ):
        f.write(chunk)

print(f"✅ Audio saved to {output_path}")

import os
from openai import AzureOpenAI

endpoint = "https://rohan-mcj7eabj-swedencentral.cognitiveservices.azure.com/"
model_name = "text-embedding-3-small"
deployment = "text-embedding-3-small"

api_version = "2024-02-01"

# client = AzureOpenAI(
#     api_version="2024-12-01-preview",
#     endpoint=endpoint,
#     credential="3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7"
# )
client = AzureOpenAI(
    azure_endpoint=endpoint,
    api_key="3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7",
    api_version=api_version,
)

response = client.embeddings.create(
    input=["first phrase","second phrase","third phrase"],
    model=deployment
)

for item in response.data:
    length = len(item.embedding)
    print(
        f"data[{item.index}]: length={length}, "
        f"[{item.embedding[0]}, {item.embedding[1]}, "
        f"..., {item.embedding[length-2]}, {item.embedding[length-1]}]"
    )
print(response.usage)

