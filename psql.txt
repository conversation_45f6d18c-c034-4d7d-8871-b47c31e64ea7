C:\Program Files\PostgreSQL\17\bin>psql -h authenticationdb.cyvysmg4w1i4.us-east-1.rds.amazonaws.com -U postgres -d postgres -W
Password:

psql (17.4, server 17.2)
WARNING: Console code page (437) differs from Windows code page (1252)
         8-bit characters might not work correctly. See psql reference
         page "Notes for Windows users" for details.
SSL connection (protocol: TLSv1.3, cipher: TLS_AES_256_GCM_SHA384, compression: off, ALPN: postgresql)
Type "help" for help.

postgres=> \dt
             List of relations
 Schema |     Name      | Type  |  Owner
--------+---------------+-------+----------
 public | Users         | table | postgres
 public | country       | table | postgres
 public | notifications | table | postgres
 public | otp           | table | postgres
 public | states        | table | postgres
 public | users         | table | postgres
(6 rows)


postgres=> select * from Users;
 user_id | title | first_name | middle_name | last_name | gender | email | password_hash | roles | profile_picture | is_active | last_login | is_deleted | created_at | updated_at
---------+-------+------------+-------------+-----------+--------+-------+---------------+-------+-----------------+-----------+------------+------------+------------+------------
(0 rows)


postgres=> select * from Country;
 country_code | country_name | country_isd | status
--------------+--------------+-------------+--------
(0 rows)


postgres=> select * from notifications;
 notification_id | sender_id | receiver_id | message_text | message_type | status | scheduled_at | expiry_date | created_at | attachment_url
-----------------+-----------+-------------+--------------+--------------+--------+--------------+-------------+------------+----------------
(0 rows)


postgres=> select * users;
ERROR:  syntax error at or near "users"
LINE 1: select * users;
                 ^
postgres=> select * from users;
 user_id | title | first_name | middle_name | last_name | gender | email | password_hash | roles | profile_picture | is_active | last_login | is_deleted | created_at | updated_at
---------+-------+------------+-------------+-----------+--------+-------+---------------+-------+-----------------+-----------+------------+------------+------------+------------
(0 rows)


postgres=>CREATE TABLE investor_details (company_name TEXT,company_email TEXT,company_type TEXT, linkedin TEXT,twitter TEXT,crunchbase TEXT,other_links TEXT,who_are_we TEXT,location TEXT,check_size_ranges TEXT,investment_rounds TEXT,lead_rounds TEXT,sectors_invested TEXT,geographies_invested TEXT, team_members TEXT, member_link TEXT);


Prasha_Health#  \COPY investor_details FROM 'D:/PrashaSync/web_scrap/investor_final.csv' WITH CSV HEADER ENCODING 'UTF8';




az container create `  --resource-group myResourceGroup `  --name myappcontainer `  --image chatfinal.azurecr.io/prasha-chat-ai4:latest `
  --cpu 2 --memory 3 `  --registry-login-server chatfinal.azurecr.io `  --registry-username chatfinal `  --registry-password "****************************************************" `  --dns-name-label prasha-chat-final `  --ports 8000 `  --os-type Linux `  --environment-variables `    OPENAI_API_KEY="***********************************************************************************************************************************************************************" `    HF_API_URL="https://router.huggingface.co/hf-inference/models/SamLowe/roberta-base-go_emotions" `    HF_API_KEY="************************************* `    postgres_username="postgres" `    postgres_password="Prashaind2025" `   postgres_host="authenctication.cmb684u0s8ql.us-east-1.rds.amazonaws.com" `    postgres_port="5432" `    postgres_database="postgres" `    USE_S3="True" `    PDF_BUCKET_NAME="prasha-healthcare-pdf" `    AWS_ACCESS_KEY_ID="********************" `    AWS_SECRET_ACCESS_KEY="THEjwqZmsdhhAQpL9uSHfySvSGM8tZPaji6TqEvb" `    AWS_REGION="us-east-1" `    JWT_SECRET="e3ddffa7eb26539eb449c2f9fbd5bd0a566cf00bef73f37e015d826e0b602f0d" `    JWT_ALGORITHM="HS256" `  STATIC_JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4"

