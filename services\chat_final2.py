import os
import uuid
import json
import base64
import asyncio
import tempfile
import logging
import concurrent.futures
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dotenv import load_dotenv

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session
from sqlalchemy import desc, text
from asyncio import wait_for

from database.database import SessionLocal, get_db
from model.model_correct import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ient, Doctor, Appointment, OnboardingQuestion, DiaryEntry,
    EmotionAnalysis, MedicalHistory, Prescription
)

from openai import OpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from elevenlabs import ElevenLabs
# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create router
chat_final_router2 = APIRouter()

from openai import AzureOpenAI

endpoint = "https://mentalhealth-bot.openai.azure.com/"
model_name = "gpt-4o"
deployment = "gpt-4o"

subscription_key = "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL"
api_version = "2024-12-01-preview"

from elevenlabs.client import ElevenLabs

ElevenLabs_client = ElevenLabs(
    api_key="***************************************************",  # 🔑
)

client = AzureOpenAI(
    api_version=api_version,
    azure_endpoint=endpoint,
    api_key=subscription_key,
)
OPENAI_audio_CLIENT = AzureOpenAI(
    api_key="3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7",
    azure_endpoint="https://rohan-mcj7eabj-swedencentral.cognitiveservices.azure.com/",
    api_version="2025-03-01-preview"
)
# Initialize OpenAI client
try:
    # Force reload the API key from environment
    load_dotenv(override=True)
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

    if OPENAI_API_KEY:
        logger.info(f"Initializing OpenAI client with API key: {OPENAI_API_KEY[:10]}...{OPENAI_API_KEY[-5:]}")
        OPENAI_CLIENT = OpenAI(api_key=OPENAI_API_KEY)
    else:
        logger.warning("WARNING: OpenAI API key not found in environment variables.")
        OPENAI_CLIENT = None

    if not OPENAI_CLIENT:
        logger.warning("WARNING: OpenAI client could not be initialized. Check your API key.")
except ImportError:
    logger.warning("WARNING: OpenAI package not installed. Some features may not work.")
    OPENAI_CLIENT = None
# OPENAI_CLIENT=client

# AI Doctor ID (fixed ID for the AI doctor)
# AI_DOCTOR_ID = "00000000-0000-0000-0000-000000000000"

# S3 paths for different FAISS indexes - confirmed with S3 bucket listing
S3_PATHS = {
    "psychologist": "faiss_index/General_faiss_index",  # Using general index for psychologist
    "dietician": "faiss_index/Deiticien_faiss_index"
    # Removed PDF path to avoid unnecessary downloads
}

# Dictionary to cache vector stores
_vector_stores = {
    "psychologist": None,
    "dietician": None
    # Removed PDF vector store to avoid unnecessary downloads
}

# S3 Configuration
USE_S3 = os.getenv("USE_S3", "true").lower() == "true"  # Default to true
S3_BUCKET = os.getenv("PDF_BUCKET_NAME", "prasha-health-pdf")  # Use the correct bucket name
AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY_ID")  # Use the correct environment variable name
AWS_SECRET_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")  # Use the correct environment variable name
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")



PSYCHOLOGIST_PROMPT = """
You are a psychologist named 'Ori' with 15 years of experience,
you are working for a PrashaSync, which is an AI-driven platform that revolutionizes how people access and receive mental healthcare through personalized therapeutic experiences.
You are having a warm, casual, and supportive conversation with a patient. Your primary goal is to connect empathetically and help them feel heard and understood,
just like you would in a relaxed chat in your office.

IMPORTANT: You have been provided with:
1. The patient's medical records and history from the database
2. The patient's onboarding health information and questionnaire responses
3. The patient's previous conversation with you
4. Relevant medical information retrieved from our knowledge base

Review this information carefully to understand the patient's background, concerns, and why they were
referred to you. Pay special attention to any mental health history, medications, previous diagnoses,
and the concerns they expressed to you.

Focus on mental health concerns, emotional well-being, and psychological symptoms. Provide thoughtful,
evidence-based guidance while maintaining a compassionate approach.


# TIP's for perfect response
- keep responses short and to the point dont create longer responses, keep like a real conversation
- If it is about therapy or treatment then suggest user a treatment plan go step by step, try to understand the concern and guide or suggest therapy to them
- or when you expect your response to be long, break the answer into a clear step-by-step plan. 
- if you think response will be longer then only use formatting like bullet points or numbers, otherwise keep it simple and plain text
- never say I can't provide you therapy and look for a therapist, keep confidence in you , you are a great psychologist-
- Do not hallucinate look for chat history, or context related to chat and try to answer in a confident manner.

❗Reply ONLY in this JSON structure:
{
  "response": "Your empathetic and helpful response to the patient",
  "extracted_keywords": ["keyword1", "keyword2"], #based on user input keywords related to mood or mental health

}

Only output the JSON (no explanation, no markdown).

"""


DIETICIAN_PROMPT = """
You are a dietician named Maya with 15 years of experience specializing in nutritional counseling and dietary management.
you are working for a PrashaSync, which is an AI-driven platform that revolutionizes how people access and receive mental healthcare through personalized therapeutic experiences.
You provide expert advice on nutrition, diet plans, and healthy eating habits.

IMPORTANT: You have been provided with:
1. The patient's medical records and history from the database
2. The patient's onboarding health information and questionnaire responses
3. The patient's previous conversation with you
4. Relevant nutritional and medical information retrieved from our knowledge base

Review this information carefully to understand the patient's background, concerns, and why they were
referred to you. Pay special attention to their weight history, dietary habits, food allergies or intolerances,
medical conditions that affect nutrition (like diabetes or hypertension), and any nutritional concerns
they expressed to you.

Focus on personalized nutritional needs, evidence-based dietary recommendations, and practical meal planning
advice. Consider the patient's lifestyle, preferences, and medical conditions when providing guidance.
Maintain a supportive and encouraging approach.



Tips:-
(keep responses short and to the point dont create longer responses, keep like a real conversation
When the user asks for a diet plan, generate a concise response. Ask for the diet preference and generate a good user friendly diet.
if you think response will be longer then only use formatting like bullet points or numbers, otherwise keep it simple and plain text)


❗Reply ONLY in this JSON structure:
{
  "response": "Your professional and helpful response to the patient",
  "extracted_keywords": ["keyword1", "keyword2"], #based on user input keywords related to nutrition or diet

}

Only output the JSON (no explanation, no markdown).

"""


# Use pre-loaded vector stores from the initializer
import vector_store_initializer1

# Get vector stores from the initializer
logger.info("Using pre-loaded vector stores from initializer...")
psychologist_vector_store = vector_store_initializer1.psychologist_vector_store
dietician_vector_store = vector_store_initializer1.dietician_vector_store
# Removed pdf_vector_store to avoid unnecessary downloads

# Get retrievers from the initializer
psychologist_retriever = vector_store_initializer1.psychologist_retriever
dietician_retriever = vector_store_initializer1.dietician_retriever
# Removed pdf_retriever to avoid unnecessary downloads

logger.info("Successfully accessed all pre-loaded vector stores")

# Create a thread pool executor for parallel operations
# Using max_workers=None will use the default value (min(32, os.cpu_count() + 4))
thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=None)

# Register shutdown handler to clean up resources
import atexit

@atexit.register
def cleanup_resources():
    """Clean up resources when the application shuts down."""
    logger.info("Shutting down thread pool...")
    thread_pool.shutdown(wait=True)
    logger.info("Thread pool shutdown complete.")

# Function to retrieve information from vector store
def retrieve_information(query, retriever):
    """Retrieve relevant information from a vector store."""
    try:
        docs = retriever.get_relevant_documents(query)
        return [doc.page_content for doc in docs]
    except Exception as e:
        logger.error(f"Error retrieving information: {str(e)}")
        return []

# Function to get patient information
def get_patient_info(patient_id: str, db: Session) -> Dict:
    """Get patient information from the database."""
    try:
        patient = db.query(Patient).filter(Patient.patient_id == patient_id).first()
        if not patient:
            return {"error": "Patient not found"}

        # Get medical history
        medical_history = db.query(MedicalHistory).filter(
            MedicalHistory.patient_id == patient_id
        ).order_by(desc(MedicalHistory.created_at)).all()

        # Get prescriptions
        prescriptions = db.query(Prescription).filter(
            Prescription.patient_id == patient_id
        ).order_by(desc(Prescription.created_at)).all()

        # Get onboarding questions and answers
        onboarding_questions = db.query(OnboardingQuestion).filter(
            OnboardingQuestion.patient_id == patient_id
        ).order_by(OnboardingQuestion.timestamp).all()

        # Get appointments using raw SQL to avoid ORM column mismatch issues
        appointments_query = """
            SELECT
                appointment_id,
                patient_id,
                doctor_id,
                appointment_date,
                appointment_time,
                visit_reason,
                status,
                notes,
                created_at,
                updated_at
            FROM appointments
            WHERE patient_id = :patient_id
            ORDER BY appointment_date DESC
        """
        appointments = db.execute(
            text(appointments_query),
            {"patient_id": patient_id}
        ).fetchall()

        # Calculate age from date of birth
        age = None
        if patient.dob:
            today = datetime.now()
            age = today.year - patient.dob.year - ((today.month, today.day) < (patient.dob.month, patient.dob.day))

        # Format patient info
        patient_info = {
            "patient_id": str(patient.patient_id),  # Convert UUID to string
            "name": f"{patient.first_name} {patient.last_name}",
            "gender": patient.gender,
            "dob": patient.dob.strftime("%Y-%m-%d") if patient.dob else None,
            "age": age,  # Add calculated age
            "medical_history": [
                {
                    "condition": mh.diagnosis,  # Using diagnosis instead of condition
                    "notes": mh.additional_notes or mh.treatment or "",  # Using additional_notes or treatment as fallback
                    "date": mh.diagnosed_date.strftime("%Y-%m-%d") if mh.diagnosed_date else (
                        mh.created_at.strftime("%Y-%m-%d") if mh.created_at else None
                    )
                } for mh in medical_history
            ],
            "prescriptions": [
                {
                    "medication": p.medication_name,  # Using medication_name instead of medication
                    "dosage": p.dosage,
                    "instructions": p.instructions,
                    "date": p.start_date.strftime("%Y-%m-%d") if p.start_date else (
                        p.created_at.strftime("%Y-%m-%d") if p.created_at else None
                    )
                } for p in prescriptions
            ],
            "appointments": [
                {
                    "date": a[3].strftime("%Y-%m-%d") if a[3] else None,  # appointment_date
                    "time": a[4].strftime("%H:%M") if a[4] else None,     # appointment_time
                    "reason": a[5],                                       # visit_reason
                    "status": a[6]                                        # status
                } for a in appointments
            ],
            "onboarding_questions": [
                {
                    "question": q.question,
                    "answer": q.answer,
                    "category": q.category,
                    "date": q.timestamp.strftime("%Y-%m-%d") if q.timestamp else None
                } for q in onboarding_questions
            ]
        }

        # Add additional patient fields if they exist
        if hasattr(patient, 'country'):
            patient_info['country'] = patient.country
        if hasattr(patient, 'timezone'):
            patient_info['timezone'] = patient.timezone
        if hasattr(patient, 'preferences'):
            patient_info['preferences'] = patient.preferences
        if hasattr(patient, 'interests'):
            patient_info['interests'] = patient.interests
        if hasattr(patient, 'treatment'):
            patient_info['treatment'] = patient.treatment
        if hasattr(patient, 'isOnboarded'):
            patient_info['isOnboarded'] = patient.isOnboarded

        return patient_info
    except Exception as e:
        logger.error(f"Error getting patient info: {str(e)}")
        return {"error": f"Error retrieving patient information: {str(e)}"}

# Function to get chat history
def get_chat_history(patient_id: str, doctor_id: str, limit: int = 20, db: Session = None) -> List[Dict]:
    """Get recent chat history between a specific patient and doctor."""
    try:
        if not db:
            db = SessionLocal()

        messages = db.query(ChatMessage).filter(
            (
                (ChatMessage.sender_id == patient_id) & (ChatMessage.receiver_id == doctor_id)
            ) | (
                (ChatMessage.sender_id == doctor_id) & (ChatMessage.receiver_id == patient_id)
            )
        ).order_by(desc(ChatMessage.createdAt)).limit(limit).all()

        messages.reverse()  # chronological order

        chat_history = []
        for msg in messages:
            role = "user" if msg.sender_id == patient_id else "assistant"
            chat_history.append({
                "role": role,
                "content": msg.message_text
            })

        return chat_history

    except Exception as e:
        logger.error(f"Error getting chat history: {str(e)}")
        return []
    finally:
        if db and db != SessionLocal():
            db.close()


async def safe_finalize_audio(websocket: WebSocket, audio_manager, response_json):
            # audio_manager=StreamingAudioManager()
            try:
                await audio_manager.final_chunk_processed.wait()
                await audio_manager.finalize_audio(websocket, response_json["response"])
            except Exception as e:
                import traceback
                logger.error(f"❌ Final audio generation failed (async): {str(e)}")
                logger.error(traceback.format_exc())
                try:
                    await websocket.send_text(json.dumps({
                        "type": "complete_audio",
                        "audio": "",
                        "text": response_json["response"],
                        "error": "Audio generation failed in background"
                    }))
                except Exception as send_error:
                    logger.warning(f"WebSocket closed before fallback complete_audio could be sent: {send_error}")   


# Function to generate AI response with streaming
async def generate_ai_response_streaming(
    user_input: str,
    patient_info: Dict,
    chat_history: List[Dict],
    system_prompt: str,
    retriever,
    websocket: WebSocket,
    current_persona: str
) -> Dict:
    """Generate AI response using OpenAI API with streaming support."""
    try:
        if not OPENAI_CLIENT:
            return {
                "response": "I'm sorry, but the AI service is currently unavailable. Please try again later.",
                "extracted_keywords": ["error", "unavailable"]
            }

        # Prepare context (same as before)
        retrieval_query = user_input

        # Extract medical info in parallel
        def extract_medical_conditions():
            if patient_info.get("medical_history"):
                medical_conditions = [item.get("condition", "") for item in patient_info.get("medical_history", [])]
                return [c for c in medical_conditions if c]
            return []

        def extract_medications():
            if patient_info.get("prescriptions"):
                medications = [item.get("medication", "") for item in patient_info.get("prescriptions", [])]
                return [m for m in medications if m]
            return []

        def extract_onboarding_answers():
            if patient_info.get("onboarding_questions"):
                health_categories = ["health", "medical", "symptoms", "lifestyle", "diet", "exercise", "mental_health"]
                answers = []
                for item in patient_info.get("onboarding_questions", []):
                    answer = item.get("answer", "")
                    category = item.get("category", "").lower()
                    if answer and (not category or category in health_categories):
                        answers.append(answer)
                return answers
            return []

        # Get medical context
        medical_conditions = extract_medical_conditions()
        medications = extract_medications()
        onboarding_answers = extract_onboarding_answers()

        # Enhance retrieval query
        if medical_conditions:
            retrieval_query += " " + " ".join(medical_conditions)
        if medications:
            retrieval_query += " " + " ".join(medications)
        if onboarding_answers:
            retrieval_query += " " + " ".join(onboarding_answers[:5])

        # Retrieve information
        relevant_info = retrieve_information(retrieval_query, retriever)

        # Format patient context
        patient_context = f"""
        PATIENT PROFILE:
        Name: {patient_info.get('name', 'Unknown')}
        Gender: {patient_info.get('gender', 'Unknown')}
        Date of Birth: {patient_info.get('dob', 'Unknown')}
        Age: {patient_info.get('age', 'Unknown')} years
        Country: {patient_info.get('country', 'Unknown')}
        Timezone: {patient_info.get('timezone', 'Unknown')}

        ONBOARDING HEALTH INFORMATION:
        {json.dumps(patient_info.get('onboarding_questions', []), indent=2)}

        MEDICAL HISTORY:
        {json.dumps(patient_info.get('medical_history', []), indent=2)}

        CURRENT MEDICATIONS:
        {json.dumps(patient_info.get('prescriptions', []), indent=2)}

        RECENT APPOINTMENTS:
        {json.dumps(patient_info.get('appointments', []), indent=2)}

        ADDITIONAL INFORMATION:
        Preferences: {json.dumps(patient_info.get('preferences', {}), indent=2)}
        Interests: {json.dumps(patient_info.get('interests', {}), indent=2)}
        Treatment: {json.dumps(patient_info.get('treatment', {}), indent=2)}
        """

        # Format relevant information
        if relevant_info:
            context = "RELEVANT MEDICAL INFORMATION FROM KNOWLEDGE BASE:\n\n"
            for i, info in enumerate(relevant_info, 1):
                context += f"--- Information {i} ---\n{info}\n\n"
        else:
            context = "No specific medical information found in knowledge base."

        # Prepare messages for OpenAI
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "system", "content": f"PATIENT DATABASE INFORMATION:\n{patient_context}"},
            {"role": "system", "content": f"KNOWLEDGE BASE INFORMATION:\n{context}"}
        ]

        # Add chat history (limited to last 10 messages)
        for msg in chat_history[-10:]:
            messages.append(msg)

        # Add current user input
        messages.append({"role": "user", "content": user_input})

        # 🚀 STREAMING IMPLEMENTATION
        logger.info("🌊 Starting streaming response generation...")

        # Determine voice for audio streaming
        if system_prompt == PSYCHOLOGIST_PROMPT:
            voice = "onyx"
            # voice= "29vD33N1CtxCmqQRPOHJ"  # Custom voice for psychologist "DREW"
        elif system_prompt == DIETICIAN_PROMPT:
            voice = "shimmer"
            # voice="ThT5KcBeYPX3keUQqHPh"  # Custom voice for dietician "Dorothi"

        # Initialize streaming audio manager
        audio_manager = StreamingAudioManager(voice)

        # Create streaming request
        stream = client.chat.completions.create(
            # model="gpt-4o-2024-11-20",
            model=deployment,
            messages=messages,
            temperature=0.1,
            max_tokens=850,
            top_p=1.0,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            response_format={"type": "json_object"},
            stream=True,
        )
        print("Stream::::::",stream)  # Debugging: print the stream object
        # Stream processing variables
        complete_response = ""
        response_text = ""
        first_chunk_sent = False
        response_started = False

        # Process streaming chunks
        streaming_complete = False

        for chunk in stream:
            # if chunk.choices[0].delta.content:
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                # logger.info(f"🔍 Chunk received: {chunk}")
                content = chunk.choices[0].delta.content
                complete_response += content

                # logger.info(f"🔍 Chunk: '{content}'")

                # 🚀 DUAL APPROACH: Stream response text + collect complete JSON
                if not response_started:
                    # Look for the start of response field
                    if '"response":' in complete_response:
                        response_started = True
                        logger.info("🌊 Response field detected, starting streaming...")

                        # Find where the actual response text starts
                        start_patterns = ['"response": "', '"response":"']
                        for pattern in start_patterns:
                            if pattern in complete_response:
                                start_idx = complete_response.find(pattern) + len(pattern)
                                initial_text = complete_response[start_idx:]

                                # Send initial text if we have any
                                if initial_text and not initial_text.startswith('"'):
                                    # Clean up any partial content
                                    clean_initial = initial_text.split('",')[0]  # Stop at first quote-comma
                                    if clean_initial and len(clean_initial) > 0:
                                        logger.info(f"🌊 Streaming initial text: '{clean_initial}'")
                                        await asyncio.sleep(1.5)
                                        await websocket.send_text(json.dumps({
                                            "type": "streaming_text",
                                            "content": clean_initial,
                                            "is_complete": False
                                        }))
                                        await audio_manager.add_text_chunk(clean_initial, websocket)
                                        response_text = clean_initial
                                break

                elif response_started and not streaming_complete:
                    # We're in streaming mode - check if this chunk ends the response
                    if '",' in content or '"}' in content:
                        # This chunk contains the end of response
                        end_markers = ['",', '"}']
                        final_chunk = content

                        for marker in end_markers:
                            if marker in content:
                                final_chunk = content.split(marker)[0]
                                break

                        if final_chunk.strip():
                            logger.info(f"🌊 Final streaming chunk: '{final_chunk}'")
                            await asyncio.sleep(1.5)
                            await websocket.send_text(json.dumps({
                                "type": "streaming_text",
                                "content": final_chunk,
                                "is_complete": True
                            }))
                            await audio_manager.add_text_chunk(final_chunk, websocket)
                            response_text += final_chunk

                        logger.info("🌊 Response streaming complete! Continuing to collect metadata...")
                        streaming_complete = True
                        # DON'T break - continue collecting the rest of the JSON
                    else:
                        # Continue streaming this chunk - allow ALL content including spaces
                        if content and content != '\n':  # Only filter newlines, allow everything else including spaces and quotes
                            # logger.info(f"🌊 Streaming chunk: '{content}'")

                            # Create the message to send
                            streaming_message = {
                                "type": "streaming_text",
                                "content": content,
                                "is_complete": False
                            }

                            # Log the exact JSON being sent
                            # logger.info(f"🔍 DEBUG: Sending streaming JSON: {json.dumps(streaming_message)}")

                            await websocket.send_text(json.dumps(streaming_message))
                            await audio_manager.add_text_chunk(content, websocket)
                            response_text += content

                # Continue collecting chunks even after streaming is complete
                # This ensures we get the full JSON with metadata

        # 🚀 PROCESS COMPLETE RESPONSE
        logger.info("🌊 Streaming complete, processing full response...")
        print("Complete_response::::::",complete_response)  # Debugging: print the complete response

       
        # Clean up and parse the full JSON
        ai_response = complete_response.strip('```json').strip('```').strip()
        ai_response = ai_response.replace('{\n', '{').replace('\n}', '}').replace(",\n", ",").replace('\n', '###').strip().replace('###', '')

        try:
            response_json = json.loads(ai_response)
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON parsing failed: {str(e)}")
            logger.error(f"🧾 Raw AI Response: {repr(ai_response)}")

            # Fallback
            response_json = {
                "response": response_text if response_text else "I apologize, but I encountered an error processing your request. Could you please rephrase?",
                "extracted_keywords": ["error"]
            }

        # Defensive check: ensure 'response' exists and is not empty
        response_text = response_json.get("response", "").strip()
        if not response_text:
            logger.warning("⚠️ Empty 'response' field in response_json. Adding fallback.")
            response_text = "I'm sorry, I didn't quite catch that. Could you please rephrase?"
            response_json["response"] = response_text

        # Ensure required fields are present
        response_json.setdefault("extracted_keywords", [])
       

        # Ensure required fields are present
        if "response" not in response_json:
            response_json["response"] = response_text if response_text else "I apologize, but I couldn't generate a proper response. Please try again."

        if "extracted_keywords" not in response_json:
            response_json["extracted_keywords"] = []

       

       

        # 🎵 Complete streaming and process final chunk
        await audio_manager.complete_streaming(websocket)

        # ✅ Wait for final chunk audio generation to finish
        await audio_manager.final_chunk_processed.wait()
        # 🔁 Offload complete audio generation to backgroun
        await asyncio.sleep(5)

        estimated_duration = len(audio_manager.all_streamed_text) * 0.01 + 2.0  # 2 second buffer 
        logger.info(f"🎵 Waiting {estimated_duration:.1f}s for streaming audio to complete before generating complete audio")
        await asyncio.sleep(estimated_duration)
        try:
            if estimated_duration < 20:
                asyncio.create_task(safe_finalize_audio(websocket, audio_manager, response_json))
            else:
                logger.info(f"⏩ Skipping finalize_audio due to short estimated duration ({estimated_duration:.2f}s)")
                await websocket.send_text(json.dumps({
                    "type": "complete_audio",
                    "audio": "",
                    "text": response_json["response"],
                    "error": "Skipped complete audio due to short duration"
                }))

        except Exception as e:
            logger.error(...)
            fallback = {
                "type": "complete_audio",
                "audio": "",
                "text": "I encountered an error processing your request. Could you rephrase?",
                "error": "Audio or websocket processing failed"
            }
            try:
                await websocket.send_text(json.dumps(fallback))
                await websocket.send_text(json.dumps({
                    "type": "streaming_complete",
                    "extracted_keywords": ["error"],
                    "current_persona": current_persona
                }))
            except:
                logger.warning("WebSocket closed before fallback could be sent.")

        await asyncio.sleep(1)

        # 

        # 🌊 Send enhanced completion signal with audio information
        await websocket.send_text(json.dumps({
            "type": "streaming_complete",
            "extracted_keywords": response_json.get("extracted_keywords", []),
            "current_persona": current_persona,
            "audio_chunks_generated": len(audio_manager.audio_queue),
            "complete_text_available": True,
            "streaming_text_length": len(audio_manager.all_streamed_text),
            "final_text_length": len(response_json["response"])
        }))


        logger.info(f"🌊 Streaming response complete. Total length: {len(response_json['response'])}")
        logger.info(f"🎵 Audio chunks generated: {len(audio_manager.audio_queue)}")
        return response_json

    except Exception as e:
        logger.error(f"Error generating streaming AI response: {str(e)}")
        # Send error to client
        await websocket.send_text(json.dumps({
            "type": "streaming_error",
            "error": str(e)
        }))
        return {
            "response": "I apologize, but I encountered an error while processing your request. Please try again.",
            "extracted_keywords": ["error"]
        }

# Function to generate AI response (non-streaming fallback)
def generate_ai_response(
    user_input: str,
    patient_info: Dict,
    chat_history: List[Dict],
    system_prompt: str,
    retriever
) -> Dict:
    """Generate AI response using OpenAI API."""
    try:
        if not OPENAI_CLIENT:
            return {
                "response": "I'm sorry, but the AI service is currently unavailable. Please try again later.",
                "extracted_keywords": ["error", "unavailable"]
            }

        # Retrieve relevant information based on both user input and patient history
        # Combine user input with key medical terms from patient history for better retrieval
        retrieval_query = user_input

        # Start parallel tasks for extracting medical conditions, medications, and onboarding answers
        # This runs the extraction in parallel with other operations
        def extract_medical_conditions():
            if patient_info.get("medical_history"):
                medical_conditions = [item.get("condition", "") for item in patient_info.get("medical_history", [])]
                return [c for c in medical_conditions if c]
            return []

        def extract_medications():
            if patient_info.get("prescriptions"):
                medications = [item.get("medication", "") for item in patient_info.get("prescriptions", [])]
                return [m for m in medications if m]
            return []

        def extract_onboarding_answers():
            if patient_info.get("onboarding_questions"):
                # Extract answers from onboarding questions, focusing on health-related categories
                health_categories = ["health", "medical", "symptoms", "lifestyle", "diet", "exercise", "mental_health"]
                answers = []

                for item in patient_info.get("onboarding_questions", []):
                    answer = item.get("answer", "")
                    category = item.get("category", "").lower()

                    # Include all answers, but prioritize health-related ones
                    if answer and (not category or category in health_categories):
                        answers.append(answer)

                return answers
            return []

        # Submit tasks to thread pool
        medical_conditions_future = thread_pool.submit(extract_medical_conditions)
        medications_future = thread_pool.submit(extract_medications)
        onboarding_answers_future = thread_pool.submit(extract_onboarding_answers)

        # Get results from futures
        medical_conditions = medical_conditions_future.result()
        medications = medications_future.result()
        onboarding_answers = onboarding_answers_future.result()

        # Enhance retrieval query with medical conditions, medications, and onboarding answers
        if medical_conditions:
            retrieval_query += " " + " ".join(medical_conditions)
        if medications:
            retrieval_query += " " + " ".join(medications)
        if onboarding_answers:
            # Add the most relevant onboarding answers to the query
            # Limit to first 5 answers to avoid making the query too long
            retrieval_query += " " + " ".join(onboarding_answers[:5])

        # Retrieve information using the enhanced query
        # This is still sequential as it depends on the enhanced query
        relevant_info = retrieve_information(retrieval_query, retriever)

        # Format patient info for context with clear sections
        patient_context = f"""
PATIENT PROFILE:
Name: {patient_info.get('name', 'Unknown')}
Gender: {patient_info.get('gender', 'Unknown')}
Date of Birth: {patient_info.get('dob', 'Unknown')}
Age: {patient_info.get('age', 'Unknown')} years
Country: {patient_info.get('country', 'Unknown')}
Timezone: {patient_info.get('timezone', 'Unknown')}

ONBOARDING HEALTH INFORMATION:
{json.dumps(patient_info.get('onboarding_questions', []), indent=2)}

MEDICAL HISTORY:
{json.dumps(patient_info.get('medical_history', []), indent=2)}

CURRENT MEDICATIONS:
{json.dumps(patient_info.get('prescriptions', []), indent=2)}

RECENT APPOINTMENTS:
{json.dumps(patient_info.get('appointments', []), indent=2)}

ADDITIONAL INFORMATION:
Preferences: {json.dumps(patient_info.get('preferences', {}), indent=2)}
Interests: {json.dumps(patient_info.get('interests', {}), indent=2)}
Treatment: {json.dumps(patient_info.get('treatment', {}), indent=2)}
"""

        # Format relevant information with clear section headers
        if relevant_info:
            context = "RELEVANT MEDICAL INFORMATION FROM KNOWLEDGE BASE:\n\n"
            for i, info in enumerate(relevant_info, 1):
                context += f"--- Information {i} ---\n{info}\n\n"
        else:
            context = "No specific medical information found in knowledge base."

        # Prepare messages for OpenAI with enhanced context
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "system", "content": f"PATIENT DATABASE INFORMATION:\n{patient_context}"},
            {"role": "system", "content": f"KNOWLEDGE BASE INFORMATION:\n{context}"}
        ]

        # Add chat history (limited to last 10 messages to save tokens)
        for msg in chat_history[-10:]:
            messages.append(msg)

        # Add current user input
        messages.append({"role": "user", "content": user_input})

        # Log messages being sent to LLM
        messages_to_log = []
        for msg in messages:
            # Create a copy of the message to avoid modifying the original
            msg_copy = msg.copy()
            # Truncate content if it's too long
            if len(msg_copy.get('content', '')) > 500:
                msg_copy['content'] = msg_copy['content'][:500] + "... [truncated]"
            messages_to_log.append(msg_copy)

        logger.info(f"Messages sent to LLM: {json.dumps(messages_to_log, indent=2)}")

        # print("Messages sent to LLM::::", messages)

        # Generate response using the same model and configuration as smart_agent.py
        response = client.chat.completions.create(
            model="gpt-4o-2024-11-20",
            messages=messages,
            temperature=0.1,
            max_tokens=850,
            top_p=1.0,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            response_format={"type": "json_object"}
        )

        # Parse response with improved error handling similar to smart_agent.py
        response_text = response.choices[0].message.content.strip()

        # Clean up the response text similar to smart_agent.py
        ai_response = response_text.strip('```json').strip('```').strip().replace('{\n','{').replace('\n}','}').replace(",\n",",").replace('\n','###')
        ai_response = ai_response.strip().replace('###', '')

        logger.info(f"Generated response: {ai_response}")

        try:
            response_json = json.loads(ai_response)
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {str(e)}")
            logger.error(f"GPT said: {repr(ai_response)}")
            # Provide a fallback response
            response_json = {
                "response": "I apologize, but I encountered an error processing your request. Could you please rephrase?",
                "extracted_keywords": ["error"]
            }

        # Log response from LLM
        logger.info(f"Response from LLM: {response_text}")

        # Ensure required fields are present
        if "response" not in response_json:
            response_json["response"] = "I apologize, but I couldn't generate a proper response. Please try again."

        if "extracted_keywords" not in response_json:
            response_json["extracted_keywords"] = []


        

        return response_json
    except Exception as e:
        logger.error(f"Error generating AI response: {str(e)}")
        return {
            "response": "I apologize, but I encountered an error while processing your request. Please try again.",
            "extracted_keywords": ["error"]
        }
import unicodedata

def remove_control_chars(text):
    return ''.join(ch for ch in text if unicodedata.category(ch)[0] != 'C')
import base64
import re
import time
import traceback
from typing import Optional

def clean_text_for_tts(text: str) -> str:
    """Remove control and invisible characters that might break TTS."""
    text = re.sub( r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]', '', text)  # control chars
    text = re.sub(r'[\u200B-\u200F\u202A-\u202E\u2060\uFEFF]', '', text)  # invisibles
    return text

def generate_audio(text: str, voice: str="onyx", max_retries: int = 2) -> Optional[str]:
    """Generate audio from text using OpenAI API with fallback handling."""
    try:
        if not OPENAI_audio_CLIENT:
            logger.error("🛑 OPENAI_CLIENT not initialized. Cannot generate audio.")
            return None

        cleaned_text = clean_text_for_tts(text)
        logger.info(f"🧪 TTS input cleaned (len={len(cleaned_text)}): {repr(cleaned_text[:200])}...")

        if len(cleaned_text) > 2000:
            logger.warning("⚠️ TTS input exceeds 4096 characters. May fail without chunking.")

        retries = 0
        while retries <= max_retries:
            try:
                response = OPENAI_audio_CLIENT.audio.speech.create(
                    model="tts",
                    voice=voice,
                    input=cleaned_text
                )

                if not response or not hasattr(response, "content"):
                    raise ValueError("No audio content returned.")

                audio_data = response.content
                base64_audio = base64.b64encode(audio_data).decode("utf-8")
                # 🔁 Generate audio
                # audio_chunks = ElevenLabs_client.text_to_speech.convert(
                #     voice_id=voice,
                #     text=cleaned_text,
                #     model_id="eleven_multilingual_v2",
                #     output_format="mp3_44100_128"
                # )

                # # 🧱 Combine chunks
                # audio_data = b"".join(audio_chunks)

                # # 🔁 Encode to base64
                # base64_audio = base64.b64encode(audio_data).decode("utf-8")

                return base64_audio

            except Exception as api_error:
                logger.error(f"❌ OpenAI TTS API failed (Attempt {retries+1}/{max_retries}): {str(api_error)}")
                logger.debug(traceback.format_exc())
                time.sleep(1.5)
                retries += 1

        logger.error("❌ All retries for TTS failed.")
        DUMMY_AUDIO_B64 = (
    "SUQzAwAAAAAAFlRFTkMAAAAwAAAACAAADSBNUE4AAAACAAACAAADAAACFNUU0UAAAAA//tQxAADBzYAGnYA"
    "AAACAAADSBNUE4AAAB9AAAACAAADSBNUE4AAAB9AAAACAAADSBNUE4AAAB9AAAACAAADSBNUE4AAAB9AAAA"
    "CAAADSBNUE4AAAB9AAAA"  # Truncated for brevity
)
        logger.warning("⚠️ Returning dummy audio fallback.")
        # return DUMMY_AUDIO_B64
        return base64.b64encode(b"\x00" * 1024).decode("utf-8")

    except Exception as e:
        logger.error(f"❌ generate_audio() internal failure: {str(e)}")
        logger.debug(traceback.format_exc())
        DUMMY_AUDIO_B64 = (
    "SUQzAwAAAAAAFlRFTkMAAAAwAAAACAAADSBNUE4AAAACAAACAAADAAACFNUU0UAAAAA//tQxAADBzYAGnYA"
    "AAACAAADSBNUE4AAAB9AAAACAAADSBNUE4AAAB9AAAACAAADSBNUE4AAAB9AAAACAAADSBNUE4AAAB9AAAA"
    "CAAADSBNUE4AAAB9AAAA"  # Truncated for brevity
)
        logger.warning("⚠️ Returning dummy audio fallback.")
        # return DUMMY_AUDIO_B64
        return base64.b64encode(b"\x00" * 1024).decode("utf-8")

import base64
import textwrap
import traceback
import re

import unicodedata
# Streaming audio generation manager
def clean_text(text):
    import re
    # Remove non-printable and problematic characters
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]', '', text)
    # Remove directional markers, soft hyphens, etc.
    text = re.sub(r'[\u200B-\u200F\u202A-\u202E\u2060\uFEFF]', '', text)
    return text
class StreamingAudioManager:
    def __init__(self, voice: str ):
        self.voice = voice
        self.accumulated_text = ""
        self.audio_queue = []
        self.is_generating = False
        self.all_streamed_text = ""  # Track all text that was streamed
        self.streaming_complete = False  # Track if streaming is complete
        self.final_chunk_processed = asyncio.Event()
        self.streamed_audio_chunks = []


    async def add_text_chunk(self, text_chunk: str, websocket: WebSocket):
        """Add text chunk and generate audio when we have enough text."""
        self.accumulated_text += text_chunk
        self.all_streamed_text += text_chunk
        self.final_chunk_processed.set()


        # Generate audio when we have a complete sentence or enough text
        # Also generate if streaming is complete (final chunk) regardless of length
        should_generate = (
            (("." in self.accumulated_text or "!" in self.accumulated_text or "?" in self.accumulated_text)
             and len(self.accumulated_text) > 15) or  # Reduced minimum length from 30 to 15
            len(self.accumulated_text) > 100 or
            (self.streaming_complete and len(self.accumulated_text.strip()) > 0)  # Generate final chunk regardless of length
        )
        

        if should_generate:

            if not self.is_generating:
                self.is_generating = True
                # Generate audio for accumulated text
                # audio_future = thread_pool.submit(generate_audio, self.accumulated_text, self.voice)

                try:
                    # audio_data = audio_future.result(timeout=10)  # 10 second timeout
                    audio_data = await asyncio.to_thread(generate_audio, self.accumulated_text, self.voice)

                    if audio_data:
                        # Determine if this is the final chunk based on streaming status
                        audio_type = "streaming_audio_final" if self.streaming_complete else "streaming_audio"

                        # Send audio chunk to client
                        await websocket.send_text(json.dumps({
                            "type": audio_type,
                            "audio": audio_data,
                            "text": self.accumulated_text,
                            "sequence": len(self.audio_queue)
                        }))

                        if self.streaming_complete:
                            await asyncio.sleep(0.5)
                            logger.info(f"🎵 Generated final streaming audio chunk")
                        else:
                            logger.info(f"🎵 Generated streaming audio for: '{self.accumulated_text[:30]}...'")

                        # Add to queue for final audio
                        self.audio_queue.append({
                            "audio": audio_data,
                            "text": self.accumulated_text,
                            "sequence": len(self.audio_queue)
                        })

                        # Reset for next chunk
                        self.accumulated_text = ""
                        if self.streaming_complete:
                            await asyncio.sleep(0.5)
                            logger.info("✅ Final audio chunk processed. Notifying waiting coroutines.")
                            self.final_chunk_processed.set()
  # Signal final chunk processed
                except Exception as e:
                    logger.error(f"Error generating streaming audio: {str(e)}")
                finally:
                    self.is_generating = False

    async def complete_streaming(self, websocket: WebSocket):
        """Mark streaming as complete and process any remaining accumulated text."""
        self.streaming_complete = True

        # Process any remaining accumulated text as final chunk
        if self.accumulated_text.strip():
            logger.info(f"🎵 Processing final accumulated text: '{self.accumulated_text}'")
            # Force generation of final chunk by calling add_text_chunk with the current text
            await self.add_text_chunk("", websocket)  # This will trigger final processing due to streaming_complete flag

    
    async def finalize_audio(self, websocket: WebSocket, complete_text: str):
        """Generate complete response audio for the play button with fallback dummy."""
        try:
            def clean_text(text):
                text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]', '', text)
                text = re.sub(r'[\u200B-\u200F\u202A-\u202E\u2060\uFEFF]', '', text)
                return text

            sanitized_text = clean_text(complete_text)
            logger.info(f"🎵 Finalizing audio for complete text (length: {len(sanitized_text)})")

            if len(sanitized_text) > 1500:

                logger.warning("⚠️ Text length exceeds 500 characters. Skipping audio generation and sending dummy response.")
                await websocket.send_text(json.dumps({
                    "type": "complete_audio",
                    "audio": "",
                    "text": sanitized_text,
                    "error": "Skipped audio generation due to text length > 500",
                    "complete_text_length": len(sanitized_text)
                }))
                return  # Exit early

            CHUNK_SIZE = 400
            chunks = textwrap.wrap(sanitized_text, width=CHUNK_SIZE, break_long_words=False, replace_whitespace=False)

            merged_audio_bytes = b""

            for i, chunk in enumerate(chunks):
                try:
                    logger.info(f"🔊 Generating TTS chunk {i+1}/{len(chunks)} (len={len(chunk)})")
                    logger.debug(f"📦 Chunk content: {repr(chunk)}")

                    audio_b64 = await asyncio.to_thread(generate_audio, chunk, self.voice)

                    if audio_b64:
                        audio_bytes = base64.b64decode(audio_b64)
                        merged_audio_bytes += audio_bytes
                        self.streamed_audio_chunks.append(audio_bytes)
                    else:
                        logger.warning(f"⚠️ Audio generation failed for chunk {i+1}")
                except Exception as chunk_error:
                    logger.error(f"❌ Error in TTS chunk {i+1}: {str(chunk_error)}")
                    logger.error(traceback.format_exc())

            if merged_audio_bytes:
                complete_audio_b64 = base64.b64encode(merged_audio_bytes).decode("utf-8")
                await websocket.send_text(json.dumps({
                    "type": "complete_audio",
                    "audio": complete_audio_b64,
                    "text": sanitized_text,
                    "total_chunks": len(self.streamed_audio_chunks),
                    "streaming_text_length": len(self.all_streamed_text),
                    "complete_text_length": len(sanitized_text)
                }))
                logger.info(f"✅ Sent complete merged audio (size: {len(complete_audio_b64)} chars)")
            else:
                logger.warning("⚠️ No audio generated. Sending dummy complete_audio response.")
                await websocket.send_text(json.dumps({
                    "type": "complete_audio",
                    "audio": "",
                    "text": sanitized_text,
                    "error": "Audio processing failed",
                    "complete_text_length": len(sanitized_text)
                }))

        except Exception as e:
            logger.error(f"❌ Error in finalize_audio: {str(e)}")
            logger.error(traceback.format_exc())
            await websocket.send_text(json.dumps({
                "type": "complete_audio",
                "audio": "",
                "text": complete_text,
                "error": f"Unexpected error: {str(e)}"
            }))



    async def get_complete_streaming_audio(self, websocket: WebSocket):
        """Send all streaming audio chunks in sequence for complete playback."""
        if self.audio_queue:
            try:
                audio_sequence_b64 = [
                    base64.b64encode(chunk).decode("utf-8")
                    for chunk in self.audio_queue
                    if chunk
                ]
                await websocket.send_text(json.dumps({
                    "type": "complete_streaming_audio_sequence",
                    "audio_sequence": audio_sequence_b64,
                    "total_duration_estimate": len(self.all_streamed_text) * 0.1  # Approximate
                }))
                logger.info(f"✅ Sent complete streaming audio sequence ({len(audio_sequence_b64)} chunks)")
            except Exception as e:
                logger.error(f"❌ Failed to send complete streaming audio: {str(e)}")
                logger.error(traceback.format_exc())
                await websocket.send_text(json.dumps({
                    "type": "complete_streaming_audio_sequence",
                    "audio_sequence": [],
                    "error": "Failed to prepare audio sequence"
                }))



# WebSocket endpoint for chat
@chat_final_router2.websocket("/chat-final2/{patient_id}")
async def chat_websocket(websocket: WebSocket, patient_id: str, db: Session = Depends(get_db)):
    await websocket.accept()
    current_persona = "psychologist"
    # Initialize connection state
    # current_persona = json.loads("persona")
    # data_json = json.loads(data)
    # current_persona = data_json.get("persona", "psychologist") 
    # if current_persona == "psychologist":
    #     AI_DOCTOR_ID = "00000000-0000-0000-0000-000000000000"
    # elif current_persona=="":
    #     AI_DOCTOR_ID = "11111111-1111-1111-1111-1111"

    authenticated = False

    # Pre-loaded data containers (will be populated after authentication)
    patient_info = None
    chat_history = []  # In-memory chat history
    print(patient_info)

    # Performance tracking
    import time
    connection_start = time.time()

    try:
        # Wait for authentication
        while not authenticated:
            try:
                logger.info("🔒 Waiting for authentication message...")
                auth_message = await websocket.receive_text()
                logger.info(f"📨 Received message: {auth_message[:100]}...")  # Log first 100 chars
                
                # Parse the message
                try:
                    auth_data = json.loads(auth_message)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON decode error: {e}")
                    await websocket.send_text(json.dumps({
                        "error": "Invalid JSON format in authentication"
                    }))
                    continue
                
                # ⚠️ CRITICAL: Handle heartbeat pings during authentication
                if auth_data.get("type") == "ping":
                    logger.info("💓 Received ping during authentication - sending pong and continuing to wait for auth")
                    await websocket.send_text(json.dumps({"type": "pong"}))
                    continue  # Continue waiting for actual authentication
                
                # ⚠️ CRITICAL: Handle heartbeat pongs during authentication  
                if auth_data.get("type") == "pong":
                    logger.info("💓 Received pong during authentication - continuing to wait for auth")
                    continue  # Continue waiting for actual authentication
                
                logger.info(f"🔐 Processing authentication data: {auth_data}")
                
                # Check for token
                token = auth_data.get("token")
                if not token or token == "invalid":
                    logger.warning(f"❌ Invalid or missing token: {token}")
                    await websocket.send_text(json.dumps({
                        "error": "Invalid or missing authentication token"
                    }))
                    continue

                logger.info("✅ Token validated successfully")

                # Start with default persona (psychologist)
                # Persona will be handled by frontend persona switching
                logger.info(f"👤 Starting with default persona: {current_persona}")

                # Set doctor ID by persona
                if current_persona == "psychologist":
                    AI_DOCTOR_ID = "00000000-0000-0000-0000-000000000000"
                else:  # dietician
                    AI_DOCTOR_ID = "11111111-1111-1111-1111-111111111111"

                # ✅ Authentication successful
                authenticated = True
                logger.info(f"🎉 Authentication successful for patient {patient_id} with persona {current_persona}")
                
                # Pre-load patient data
                logger.info(f"🔄 Pre-loading patient data for {patient_id}...")
                data_load_start = time.time()
                
                patient_info = get_patient_info(patient_id, db)
                if "error" in patient_info:
                    await websocket.send_text(json.dumps({
                        "error": patient_info["error"]
                    }))
                    return
                
                chat_history = get_chat_history(patient_id, AI_DOCTOR_ID, limit=20, db=db)
                data_load_time = time.time() - data_load_start
                logger.info(f"✅ Patient data loaded in {data_load_time:.2f}s - Patient: {patient_info['name']}, History: {len(chat_history)} messages")
                
                # Set prompts and voice based on persona
                if current_persona == "psychologist":
                    system_prompt = PSYCHOLOGIST_PROMPT
                    retriever = psychologist_retriever
                    voice = "onyx"
                    welcome_prompt = (
                        "Hello, Dr. Ori You are now talking to patient, First Introduce Yourself and welcome user with Welcome message"
                        if len(chat_history) == 0 else
                        "Hello, Dr. Ori you are now talking to a patient, Look for the patients information, recent activities and chat where you left and create a short summary for him as a welcome message"
                    )
                else:  # dietician
                    system_prompt = DIETICIAN_PROMPT
                    retriever = dietician_retriever
                    voice = "shimmer"
                    welcome_prompt = (
                        "Hello, Dr. Maya You are now talking to patient, First Introduce Yourself and welcome user with Welcome message"
                        if len(chat_history) == 0 else
                        "Hello, Dr. Maya you are now talking to a patient, Look for the patients information, recent activities and chat where you left and create a short summary for him as a welcome message"
                    )
                
                # Send welcome message
                logger.info("📝 Generating welcome message...")
                response_data = await wait_for(
                    generate_ai_response_streaming(
                        welcome_prompt,
                        patient_info,
                        chat_history,
                        system_prompt,
                        retriever,
                        websocket,
                        current_persona
                    ),
                    timeout=90
                )
                await asyncio.sleep(2)
                
            except asyncio.TimeoutError:
                logger.error("⏰ Authentication timeout")
                await websocket.send_text(json.dumps({
                    "error": "Authentication timeout"
                }))
                return
            except Exception as e:
                logger.error(f"❌ Authentication error: {str(e)}")
                await websocket.send_text(json.dumps({
                    "error": f"Authentication error: {str(e)}"
                }))
                continue  # Continue waiting for valid authentication
        
        # Main chat loop starts here after successful authentication
        logger.info("🚀 Entering main chat loop...")
        

        # Main chat loop
        while True:
            # Receive message
            message = await websocket.receive_text()
            data = json.loads(message)

            # ✅ Handle heartbeat ping
            if data.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
                continue

            # ✅ Handle persona switching
            if data.get("type") == "switch_persona":
                new_persona = data.get("persona")
                if new_persona in ["psychologist", "dietician"]:
                    logger.info(f"🔄 Switching persona from {current_persona} to {new_persona}")
                    current_persona = new_persona

                    # Update AI_DOCTOR_ID based on new persona
                    if current_persona == "psychologist":
                        AI_DOCTOR_ID = "00000000-0000-0000-0000-000000000000"
                    else:  # dietician
                        AI_DOCTOR_ID = "11111111-1111-1111-1111-111111111111"

                    # Send confirmation
                    await websocket.send_text(json.dumps({
                        "type": "persona_switched",
                        "persona": current_persona,
                        "message": f"Switched to {current_persona}"
                    }))

                    # Send welcome message from new persona
                    if current_persona == "psychologist":
                        system_prompt = PSYCHOLOGIST_PROMPT
                        retriever = psychologist_retriever
                        voice = "onyx"
                        welcome_msg = "Hello, I'm Dr. Ori, your psychologist. How can I help you today?"
                    else:  # dietician
                        system_prompt = DIETICIAN_PROMPT
                        retriever = dietician_retriever
                        voice = "shimmer"
                        welcome_msg = "Hello, I'm Maya, your dietician. How can I assist you with your nutrition today?"

                    # Generate welcome message from new persona
                    try:
                        response_data = await wait_for(
                            generate_ai_response_streaming(
                                welcome_msg,
                                patient_info,
                                chat_history,
                                system_prompt,
                                retriever,
                                websocket,
                                current_persona
                            ),
                            timeout=60
                        )
                        await asyncio.sleep(2)
                    except Exception as e:
                        logger.error(f"Error generating welcome message: {str(e)}")

                    continue

            # Handle text input
            if "text" in data:
                # 🚀 OPTIMIZATION: Start timing for this request
                request_start = time.time()
                user_input = data["text"]

                # Set voice based on current persona
                if current_persona == "psychologist":
                    system_prompt = PSYCHOLOGIST_PROMPT
                    retriever = psychologist_retriever
                    # voice = "29vD33N1CtxCmqQRPOHJ"
                    voice ="onyx"
                    
                elif current_persona == "dietician":
                    system_prompt = DIETICIAN_PROMPT
                    retriever = dietician_retriever
                    # voice = "ThT5KcBeYPX3keUQqHPh"
                    voice= "shimmer"

                audio_manager = StreamingAudioManager(voice)

                logger.info(f"🎯 Processing text input: '{user_input[:50]}...' (Persona: {current_persona})")

                # 🚀 OPTIMIZATION: Start database save in parallel (non-blocking)
                def save_user_message_to_database():
                    try:
                        with SessionLocal() as thread_db:
                            now = datetime.now()
                            thread_db.add(ChatMessage(
                                chat_message_id=str(uuid.uuid4()),
                                sender_id=patient_id,
                                receiver_id=AI_DOCTOR_ID,
                                message_text=user_input,
                                createdAt=now,
                            ))
                            thread_db.commit()
                            logger.info("✅ Saved user message to database")
                            return True
                    except Exception as e:
                        logger.error(f"❌ Error saving user message: {str(e)}")
                        return False

                # Submit database task to thread pool (non-blocking)
                db_future = thread_pool.submit(save_user_message_to_database)

                # 🚀 STREAMING: Generate AI response with streaming support
                try:
                        
                    ai_start = time.time()
                    response_data = await wait_for(
                        generate_ai_response_streaming(
                        user_input,
                        patient_info,  # Using pre-loaded patient info
                        chat_history,  # Using in-memory chat history
                        system_prompt,
                        retriever,
                        websocket,  # Pass websocket for streaming
                        current_persona  # Pass current persona
                    ), timeout=90)
                    ai_time = time.time() - ai_start
                    logger.info(f"🌊 Streaming AI response completed in {ai_time:.2f}s")
                    await asyncio.sleep(2)  # Let the client play the last audio chunk smoothly
                except asyncio.TimeoutError:
                    logger.error("⏰ Timeout during generate_ai_response_streaming")

                    fallback_response = {
                        "type": "complete_audio",
                        "audio": "",
                        "text": "I’m sorry, I took too long to respond. Could you please repeat that?",
                        "error": "Timeout occurred while processing the response"
                    }

                    try:
                        await websocket.send_text(json.dumps(fallback_response))
                        await websocket.send_text(json.dumps({
                            "type": "streaming_complete",
                            "extracted_keywords": ["error", "timeout"],
                            "current_persona": current_persona
                        }))
                    except Exception as e:
                        logger.warning("WebSocket closed before fallback could be sent: %s", e)



                # 🚀 STREAMING COMPLETE: Add AI response to chat history and save to database
                response_id = str(uuid.uuid4())

                # Add AI response to in-memory chat history immediately
                chat_history.append({"role": "assistant", "content": response_data["response"]})

                def save_ai_response_to_database():
                    try:
                        with SessionLocal() as thread_db:
                            now = datetime.now()
                            thread_db.add(ChatMessage(
                                chat_message_id=response_id,
                                sender_id=AI_DOCTOR_ID,
                                receiver_id=patient_id,
                                message_text=response_data["response"],
                                extracted_keywords=",".join(response_data["extracted_keywords"]),
                                createdAt=now,
                            ))
                            thread_db.commit()
                            logger.info("✅ Saved AI response to database")
                            return True
                    except Exception as e:
                        logger.error(f"❌ Error saving AI response: {str(e)}")
                        return False

                # 🚀 OPTIMIZATION: Save to database in background
                db_future = thread_pool.submit(save_ai_response_to_database)

                # Calculate total request time
                total_time = time.time() - request_start
                logger.info(f"🏁 Total streaming request processed in {total_time:.2f}s (AI: {ai_time:.2f}s)")


            # Handle audio input
            elif "audio" in data:
                # 🚀 OPTIMIZATION: Start timing for audio processing
                audio_request_start = time.time()
                audio_base64 = data["audio"]

                try:
                    logger.info(f"🎤 Processing audio input...")
                    logger.info(f"Decoding audio Base64...")
                    audio_bytes = base64.b64decode(audio_base64)
                    logger.info(f"Base64 decoded. Creating temporary file...")

                    with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as temp_file:
                        temp_file.write(audio_bytes)
                        temp_file_path = temp_file.name
                    logger.info(f"Temporary file created at: {temp_file_path}. Starting transcription...")

                    transcription_start = time.time()

                    # --- CRITICAL FIX/OPTIMIZATION: Run transcription in thread pool ---
                    def transcribe_audio_sync(file_path):
                        with open(file_path, "rb") as audio_file:
                            return client.audio.transcriptions.create(model="whisper-1", file=audio_file).text

                    # Submit transcription to the thread pool to avoid blocking the event loop
                    loop = asyncio.get_running_loop()
                    user_input = await loop.run_in_executor(thread_pool, transcribe_audio_sync, temp_file_path)
                    # --- END CRITICAL FIX/OPTIMIZATION ---

                    logger.info(f"Transcription complete. Cleaning up temporary file...")
                    os.unlink(temp_file_path) # Clean up temporary file

                    transcription_time = time.time() - transcription_start
                    logger.info(f"🎯 Audio transcribed in {transcription_time:.2f}s: '{user_input[:50]}...'")
                    # logger.info(f"🎤 Processing audio input...")

                    # Send transcription back to client
                    await websocket.send_text(json.dumps({
                        "transcription": user_input
                    }))

                    if current_persona == "psychologist":
                        system_prompt = PSYCHOLOGIST_PROMPT
                        retriever = psychologist_retriever
                        # voice = "29vD33N1CtxCmqQRPOHJ"  # Male voice for psychologist Doctor Ori
                        voice="onyx"
                    elif current_persona == "dietician":
                        system_prompt = DIETICIAN_PROMPT
                        retriever = dietician_retriever
                        voice = "shimmer"  # Female voice for dietician Doctor Maya
                        # voice= "ThT5KcBeYPX3keUQqHPh"

                    audio_manager = StreamingAudioManager(voice)

                    # Save user message to database in parallel
                    def save_user_message_to_database():
                        try:
                            # Create a new session for this thread
                            with SessionLocal() as thread_db:
                                # Set timestamp, createdAt,
                                now = datetime.now()
                                thread_db.add(ChatMessage(
                                    chat_message_id=str(uuid.uuid4()),
                                    sender_id=patient_id,
                                    receiver_id=AI_DOCTOR_ID,
                                    message_text=user_input,
                                    # timestamp=now,
                                    createdAt=now,  # Required non-null field in the database
                                      # Required non-null field in the database
                                ))
                                thread_db.commit()
                                logger.info("✅ Saved user message to database")
                                return True
                        except Exception as e:
                            logger.error(f"❌ Error saving user message: {str(e)}")
                            return False

                    # Submit database task to thread pool
                    db_future = thread_pool.submit(save_user_message_to_database)

                    # 🚀 STREAMING: Generate AI response with streaming support
                    try:
                        ai_start = time.time()
                        
                        response_data = await wait_for( generate_ai_response_streaming(
                            user_input,
                            patient_info,  # Using pre-loaded patient info
                            chat_history,  # Using in-memory chat history
                            system_prompt,
                            retriever,
                            websocket,  # Pass websocket for streaming
                            current_persona  # Pass current persona
                        ), timeout=90)
                        ai_time = time.time() - ai_start
                        logger.info(f"🌊 Streaming AI response completed in {ai_time:.2f}s")

                        await asyncio.sleep(2)  # Let the client play the last audio chunk smoothly
                    except asyncio.TimeoutError:
                        logger.error("⏰ Timeout during generate_ai_response_streaming")

                        fallback_response = {
                            "type": "complete_audio",
                            "audio": "",
                            "text": "I’m sorry, I took too long to respond. Could you please repeat that?",
                            "error": "Timeout occurred while processing the response"
                        }

                        try:
                            await websocket.send_text(json.dumps(fallback_response))
                            await websocket.send_text(json.dumps({
                                "type": "streaming_complete",
                                "extracted_keywords": ["error", "timeout"],
                            
                            }))
                        except Exception as e:
                            logger.warning("WebSocket closed before fallback could be sent: %s", e)


                    # 🚀 STREAMING COMPLETE: Add AI response to chat history and save to database
                    chat_history.append({"role": "assistant", "content": response_data["response"]})

                    # 🚀 OPTIMIZATION: Save to database in background
                    response_id = str(uuid.uuid4())

                    def save_ai_response_to_database():
                        try:
                            with SessionLocal() as thread_db:
                                now = datetime.now()
                                thread_db.add(ChatMessage(
                                    chat_message_id=response_id,
                                    sender_id=AI_DOCTOR_ID,
                                    receiver_id=patient_id,
                                    message_text=response_data["response"],
                                    extracted_keywords=",".join(response_data["extracted_keywords"]),
                                    createdAt=now,
                                ))
                                thread_db.commit()
                                logger.info("✅ Saved AI response to database")
                                return True
                        except Exception as e:
                            logger.error(f"❌ Error saving AI response: {str(e)}")
                            return False

                    # Save to database in background
                    db_future = thread_pool.submit(save_ai_response_to_database)

                    # Calculate total audio request time
                    total_audio_time = time.time() - audio_request_start
                    logger.info(f"🏁 Total audio streaming request processed in {total_audio_time:.2f}s (Transcription: {transcription_time:.2f}s, AI: {ai_time:.2f}s)")

                
                except Exception as e:
                    logger.error("❌ Error processing audio: %s", str(e))
                    logger.error(traceback.format_exc())
                    await websocket.send_text(json.dumps({
                        "type": "complete_audio",
                        "audio": "",
                        "text": "",
                        "error": f"Audio generation failed: {str(e)}"
                    }))

            
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for patient {patient_id}")
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        try:
            await websocket.send_text(json.dumps({
                        "type": "complete_audio",
                        "audio": "",
                        "text": "",
                        "error": f"Audio generation failed: {str(e)}"
                    }))
        except:
            pass


